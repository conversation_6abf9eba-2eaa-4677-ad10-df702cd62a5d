/* MapLibre GL JS Custom Styles */

/* Map container */
.maplibregl-map {
  font-family: inherit;
}

/* Control styles */
.maplibregl-ctrl-group {
  background: rgba(26, 30, 35, 0.9) !important;
  border: 1px solid #586173 !important;
  border-radius: 4px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

.maplibregl-ctrl-group button {
  background-color: transparent !important;
  color: #F5F5F5 !important;
  border: none !important;
  width: 30px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s !important;
}

.maplibregl-ctrl-group button:hover {
  background-color: rgba(88, 97, 115, 0.3) !important;
}

.maplibregl-ctrl-group button:not(:last-child) {
  border-bottom: 1px solid #586173 !important;
}

/* Popup styles */
.maplibregl-popup {
  max-width: 300px !important;
}

.maplibregl-popup-content {
  background: #1a1e23 !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
  border-radius: 8px !important;
  padding: 0 !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.maplibregl-popup-close-button {
  color: #F5F5F5 !important;
  font-size: 18px !important;
  padding: 4px !important;
  background: transparent !important;
  border: none !important;
  cursor: pointer !important;
}

.maplibregl-popup-close-button:hover {
  background: rgba(88, 97, 115, 0.3) !important;
  border-radius: 2px !important;
}

.maplibregl-popup-tip {
  border-top-color: #1a1e23 !important;
  border-bottom-color: #1a1e23 !important;
}

/* Attribution */
.maplibregl-ctrl-attrib {
  background: rgba(26, 30, 35, 0.8) !important;
  color: #F5F5F5 !important;
  font-size: 10px !important;
}

.maplibregl-ctrl-attrib a {
  color: #0074D9 !important;
}

/* Scale control */
.maplibregl-ctrl-scale {
  background: rgba(26, 30, 35, 0.8) !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
  border-radius: 2px !important;
  padding: 2px 4px !important;
  font-size: 10px !important;
}

/* Military control container */
.military-control-container {
  background: rgba(26, 30, 35, 0.95);
  border: 1px solid #586173;
  backdrop-filter: blur(4px);
  min-width: fit-content;
}

.military-control-container .header {
  background: rgba(43, 48, 56, 0.8);
  border-bottom: 1px solid #586173;
  color: #F5F5F5;
}

.military-control-container .content {
  background: rgba(26, 30, 35, 0.95);
  color: #F5F5F5;
}

/* Control panel headers */
.military-control-container .text-xs {
  white-space: nowrap;
  min-width: 60px;
}

/* Button container alignment */
.military-control-container .flex {
  align-items: center;
  justify-content: center;
}

/* Military button styles */
.military-button {
  background: rgba(43, 48, 56, 0.8) !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
  transition: all 0.2s ease !important;
  font-family: 'Orbitron', monospace !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  width: 32px !important;
  height: 32px !important;
  min-width: 32px !important;
  min-height: 32px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  font-size: 10px !important;
}

.military-button:hover {
  background: rgba(88, 97, 115, 0.8) !important;
  border-color: #0074D9 !important;
  color: #F5F5F5 !important;
}

.military-button.active {
  background: rgba(0, 116, 217, 0.8) !important;
  border-color: #0074D9 !important;
  color: #F5F5F5 !important;
}

/* Ensure icons are properly sized within buttons */
.military-button svg {
  width: 14px !important;
  height: 14px !important;
  flex-shrink: 0 !important;
}

/* Special styling for text-only buttons (terrain controls) */
.military-button.text-button {
  font-size: 9px !important;
  font-weight: 700 !important;
}

/* Enhanced Military Marker Styles */
.incident-marker {
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.incident-marker:hover {
  transform: scale(1.15);
  z-index: 1000;
  filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.6));
}

.response-marker {
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.response-marker:hover {
  transform: scale(1.15);
  z-index: 1000;
  filter: drop-shadow(4px 4px 8px rgba(0, 0, 0, 0.6));
}

/* Enhanced tactical symbol styling */
.tactical-symbol {
  position: relative;
  z-index: 10;
  user-select: none;
  pointer-events: auto;
}

.tactical-symbol:hover {
  z-index: 1000;
}

.tactical-symbol svg {
  display: block;
  width: 100%;
  height: 100%;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.4));
}

/* Response symbol styles */
.response-symbol {
  position: relative;
  z-index: 10;
}

.response-symbol:hover {
  z-index: 20;
}

.cluster-marker {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.cluster-marker:hover {
  transform: scale(1.1);
}

/* Drawing styles */
.mapbox-gl-draw_ctrl-draw-btn {
  background: rgba(43, 48, 56, 0.8) !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
}

.mapbox-gl-draw_ctrl-draw-btn:hover {
  background: rgba(88, 97, 115, 0.8) !important;
}

.mapbox-gl-draw_ctrl-draw-btn.active {
  background: rgba(0, 116, 217, 0.8) !important;
}

/* Context menu styles */
.maplibre-context-menu {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 3D building styles */
.maplibregl-map .maplibregl-canvas-container canvas {
  outline: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .military-control-container {
    font-size: 12px;
  }

  .military-button {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    min-height: 28px !important;
    font-size: 9px !important;
  }

  .military-button svg {
    width: 12px !important;
    height: 12px !important;
  }

  .maplibregl-popup {
    max-width: 250px !important;
  }

  /* Adjust toolbar positioning for mobile */
  .toolbar-top-left {
    left: 44px; /* Adjusted for mobile */
    top: 12px;
    max-width: 160px;
  }

  .toolbar-top-right {
    top: 12px;
    right: 8px;
    max-width: 160px;
  }

  .toolbar-bottom-left {
    bottom: 80px; /* Maintain space for legend */
    left: 8px;
    max-width: 160px;
  }

  .toolbar-bottom-right {
    bottom: 8px;
    right: 8px;
    max-width: 160px;
  }

  .toolbar-bottom-center {
    bottom: 8px;
  }

  /* Stack toolbar sections more compactly on mobile */
  .toolbar-top-left .flex-col,
  .toolbar-top-right .flex-col,
  .toolbar-bottom-left .flex-col,
  .toolbar-bottom-right .flex-col {
    gap: 2px;
  }
}

@media (max-height: 600px) {
  .military-control-container {
    padding: 2px !important;
  }

  .military-button {
    width: 28px !important;
    height: 28px !important;
    min-width: 28px !important;
    min-height: 28px !important;
  }

  .military-button svg {
    width: 12px !important;
    height: 12px !important;
  }
}

/* Ensure controls don't overlap */
.map-controls-container {
  pointer-events: none;
}

.map-controls-container > * {
  pointer-events: auto;
}

/* Prevent controls from going off-screen */
.military-control-container {
  max-height: calc(100vh - 20px);
  overflow-y: auto;
  overflow-x: visible;
}

/* Four-corner toolbar layout spacing - Fixed positioning with improved spacing */
.toolbar-top-left {
  top: 16px;
  left: 60px; /* Increased offset for auto-hide toggle */
  max-width: 220px;
  min-width: 180px;
}

.toolbar-top-right {
  top: 16px;
  right: 16px;
  max-width: 220px;
  min-width: 180px;
}

.toolbar-bottom-left {
  bottom: 80px; /* Increased offset to avoid legend overlap */
  left: 16px;
  max-width: 220px;
  min-width: 180px;
}

.toolbar-bottom-right {
  bottom: 16px;
  right: 16px;
  max-width: 220px;
  min-width: 180px;
}

.toolbar-bottom-center {
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  max-width: 320px;
  min-width: 200px;
}

/* Prevent toolbar overlap with improved spacing */
.toolbar-section {
  margin: 6px 0;
  z-index: 1000;
}

.toolbar-section:not(:last-child) {
  margin-bottom: 12px;
}

/* Ensure proper spacing between collapsible sections */
.military-control-container {
  margin-bottom: 8px;
}

.military-control-container:last-child {
  margin-bottom: 0;
}

/* Prevent sections from overlapping on smaller screens */
@media (max-width: 1024px) {
  .toolbar-top-left,
  .toolbar-top-right,
  .toolbar-bottom-left,
  .toolbar-bottom-right {
    max-width: 180px;
  }

  .toolbar-bottom-left {
    bottom: 100px; /* More space for legend on smaller screens */
  }
}

/* Compact layout adjustments */
.compact-layout .military-control-container {
  margin: 1px;
}

.compact-layout .military-button {
  margin: 0;
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .incident-marker,
  .response-marker,
  .cluster-marker {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .maplibregl-ctrl-group {
    background: rgba(26, 30, 35, 0.95) !important;
  }

  .military-control-container {
    background: rgba(26, 30, 35, 0.98);
  }
}

/* Tactical Modal Enhancements */
.tactical-modal-backdrop {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.6);
}

.tactical-modal-container {
  background: rgba(26, 30, 35, 0.95);
  backdrop-filter: blur(4px);
  border: 1px solid #586173;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.tactical-modal-header {
  background: rgba(43, 48, 56, 0.8);
  border-bottom: 1px solid #586173;
}

.tactical-modal-content {
  background: rgba(26, 30, 35, 0.95);
}

/* Legend Editor Specific Styles */
.legend-item-drag {
  cursor: move;
  transition: all 0.2s ease;
}

.legend-item-drag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.legend-item-dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.legend-template-card {
  transition: all 0.2s ease;
  border: 1px solid #586173;
}

.legend-template-card:hover {
  border-color: #0074D9;
  background: rgba(88, 97, 115, 0.2);
  transform: translateY(-1px);
}

/* Symbol Manager Specific Styles */
.symbol-grid-item {
  transition: all 0.2s ease;
  border: 1px solid #586173;
}

.symbol-grid-item:hover {
  border-color: #0074D9;
  background: rgba(88, 97, 115, 0.2);
  transform: scale(1.05);
}

.symbol-grid-item.selected {
  border-color: #0074D9;
  background: rgba(0, 116, 217, 0.3);
  box-shadow: 0 0 8px rgba(0, 116, 217, 0.5);
}

.symbol-category-header {
  background: rgba(43, 48, 56, 0.6);
  border-bottom: 1px solid #586173;
}

.symbol-search-input {
  background: rgba(26, 30, 35, 0.8);
  border: 1px solid #586173;
  transition: border-color 0.2s ease;
}

.symbol-search-input:focus {
  border-color: #0074D9;
  box-shadow: 0 0 0 2px rgba(0, 116, 217, 0.2);
}

/* Tactical Form Controls */
.tactical-input {
  background: rgba(26, 30, 35, 0.8);
  border: 1px solid #586173;
  color: #F5F5F5;
  font-family: 'Courier New', monospace;
  transition: all 0.2s ease;
}

.tactical-input:focus {
  border-color: #0074D9;
  background: rgba(26, 30, 35, 0.9);
  box-shadow: 0 0 0 2px rgba(0, 116, 217, 0.2);
}

.tactical-select {
  background: rgba(26, 30, 35, 0.8);
  border: 1px solid #586173;
  color: #F5F5F5;
  font-family: 'Courier New', monospace;
}

.tactical-select:focus {
  border-color: #0074D9;
  box-shadow: 0 0 0 2px rgba(0, 116, 217, 0.2);
}

/* Color Picker Enhancements */
.color-preset-button {
  width: 24px;
  height: 24px;
  border: 2px solid #586173;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-preset-button:hover {
  transform: scale(1.1);
  border-color: #F5F5F5;
}

.color-preset-button.selected {
  border-color: #0074D9;
  box-shadow: 0 0 0 2px rgba(0, 116, 217, 0.3);
}

/* Symbol Picker Enhancements */
.symbol-option-button {
  width: 32px;
  height: 32px;
  border: 1px solid #586173;
  background: rgba(43, 48, 56, 0.6);
  color: #F5F5F5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.symbol-option-button:hover {
  background: rgba(88, 97, 115, 0.6);
  border-color: #0074D9;
}

.symbol-option-button.selected {
  background: rgba(0, 116, 217, 0.6);
  border-color: #0074D9;
  box-shadow: 0 0 0 2px rgba(0, 116, 217, 0.3);
}

/* Validation and Status Indicators */
.validation-error {
  border-color: #FF4444 !important;
  background: rgba(255, 68, 68, 0.1);
}

.validation-success {
  border-color: #00AA00 !important;
  background: rgba(0, 170, 0, 0.1);
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  text-transform: uppercase;
}

.status-indicator.error {
  background: rgba(255, 68, 68, 0.2);
  color: #FF4444;
  border: 1px solid #FF4444;
}

.status-indicator.success {
  background: rgba(0, 170, 0, 0.2);
  color: #00AA00;
  border: 1px solid #00AA00;
}

.status-indicator.warning {
  background: rgba(255, 204, 0, 0.2);
  color: #FFCC00;
  border: 1px solid #FFCC00;
}

.status-indicator.info {
  background: rgba(0, 116, 217, 0.2);
  color: #0074D9;
  border: 1px solid #0074D9;
}

/* Tactical popup styles - Military themed */
.tactical-popup-container .maplibregl-popup-content {
  background: rgba(47, 54, 47, 0.95) !important;
  border: 2px solid #5E8E3E !important;
  border-radius: 6px !important;
  padding: 0 !important;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.7) !important;
  backdrop-filter: blur(10px) !important;
  min-width: 280px !important;
  font-family: 'Courier New', monospace !important;
}

.tactical-popup-container .maplibregl-popup-tip {
  border-top-color: rgba(47, 54, 47, 0.95) !important;
  border-bottom-color: rgba(47, 54, 47, 0.95) !important;
}

.tactical-popup {
  color: #E0E0E0;
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

.tactical-popup .popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 18px;
  border-bottom: 2px solid #5E8E3E;
  background: linear-gradient(135deg, rgba(94, 142, 62, 0.3), rgba(47, 54, 47, 0.8));
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
}

.tactical-popup .popup-header h3 {
  color: #B8E6B8 !important;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  letter-spacing: 0.5px;
}

.tactical-popup .popup-content {
  padding: 16px 18px;
  background: rgba(47, 54, 47, 0.9);
}

.tactical-popup .grid-info {
  margin-bottom: 12px;
  border-left: 3px solid #5E8E3E;
  padding-left: 12px;
}

.tactical-popup .grid-info div {
  margin-bottom: 6px;
  font-size: 12px;
  color: #C8D4C8;
  font-weight: 500;
}

.tactical-popup .grid-info strong {
  color: #B8E6B8;
  font-weight: bold;
}

.tactical-popup .description {
  font-size: 12px;
  color: #C8D4C8;
  line-height: 1.5;
  background: rgba(94, 142, 62, 0.1);
  padding: 8px 12px;
  border-radius: 4px;
  border-left: 3px solid #5E8E3E;
}

/* Military-style severity and status badges */
.tactical-popup .severity-badge,
.tactical-popup .status-badge {
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8);
}

.tactical-popup .severity-critical {
  background: rgba(220, 38, 38, 0.8);
  color: #FEE2E2;
  border-color: #DC2626;
}

.tactical-popup .severity-high {
  background: rgba(245, 101, 101, 0.8);
  color: #FEE2E2;
  border-color: #F56565;
}

.tactical-popup .severity-medium {
  background: rgba(251, 191, 36, 0.8);
  color: #FEF3C7;
  border-color: #FBBF24;
}

.tactical-popup .severity-low {
  background: rgba(34, 197, 94, 0.8);
  color: #DCFCE7;
  border-color: #22C55E;
}

.tactical-popup .status-active {
  background: rgba(34, 197, 94, 0.8);
  color: #DCFCE7;
  border-color: #22C55E;
}

.tactical-popup .status-planned {
  background: rgba(59, 130, 246, 0.8);
  color: #DBEAFE;
  border-color: #3B82F6;
}

.tactical-popup .status-completed {
  background: rgba(107, 114, 128, 0.8);
  color: #F3F4F6;
  border-color: #6B7280;
}

.tactical-popup .popup-button {
  width: 100%;
  padding: 10px 16px;
  background: linear-gradient(135deg, #5E8E3E, #4A7C2A);
  border: 1px solid #5E8E3E;
  border-radius: 4px;
  color: #E0E0E0;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8);
  margin-top: 12px;
}

.tactical-popup .popup-button:hover {
  background: linear-gradient(135deg, #6B9B45, #5E8E3E);
  border-color: #6B9B45;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.tactical-popup .popup-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.severity-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
}

.severity-critical {
  background: #DC2626;
  color: white;
}

.severity-high {
  background: #EA580C;
  color: white;
}

.severity-medium {
  background: #D97706;
  color: white;
}

.severity-low {
  background: #16A34A;
  color: white;
}

.status-badge {
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  font-weight: bold;
}

.status-active {
  background: #16A34A;
  color: white;
}

.status-completed {
  background: #2563EB;
  color: white;
}

.status-cancelled {
  background: #DC2626;
  color: white;
}

/* Military symbol styles */
.military-symbol {
  transition: transform 0.2s ease;
}

.military-symbol:hover {
  transform: scale(1.1);
}

/* Viewshed marker styles */
.viewshed-marker {
  transition: transform 0.2s ease;
}

.viewshed-marker:hover {
  transform: scale(1.2);
}

/* Incident and response marker styles */
.incident-marker,
.response-marker {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.incident-marker:hover,
.response-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

/* Auto-hide toolbar animations */
.toolbar-section {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.toolbar-section.hidden {
  opacity: 0;
  pointer-events: none;
}

/* Drawing tool styles for MapLibre */
.mapbox-gl-draw_ctrl-draw-btn {
  background: rgba(26, 30, 35, 0.9) !important;
  color: #F5F5F5 !important;
  border: 1px solid #586173 !important;
}

.mapbox-gl-draw_ctrl-draw-btn:hover {
  background: rgba(88, 97, 115, 0.3) !important;
}

.mapbox-gl-draw_ctrl-draw-btn.active {
  background: rgba(0, 116, 217, 0.8) !important;
}

/* Context menu animation */
.maplibre-context-menu {
  animation: contextMenuFadeIn 0.2s ease-out;
}

@keyframes contextMenuFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
